/**
 * Brick Manager - 积木数据管理云函数
 * 提供积木数据的CRUD操作和同步功能
 */

'use strict';

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: 'zemuresume-4gjvx1wea78e3d1e'
});

const db = cloud.database();
const _ = db.command;

/**
 * 主处理函数
 */
exports.main = async (event, context) => {
  console.log('🔧 BrickManager 开始处理请求', { event });

  try {
    const { action, data, userId } = event;
    const openid = context.OPENID;

    // 验证用户身份
    if (!openid) {
      throw new Error('用户身份验证失败');
    }

    // 根据action执行不同操作
    switch (action) {
      case 'list':
        return await listBricks(openid, data);
      case 'add':
        return await addBrick(openid, data);
      case 'update':
        return await updateBrick(openid, data);
      case 'delete':
        return await deleteBrick(openid, data);
      case 'sync':
        return await syncBricks(openid, data);
      case 'get':
        return await getBrick(openid, data);
      default:
        throw new Error(`不支持的操作: ${action}`);
    }

  } catch (error) {
    console.error('❌ BrickManager 处理失败:', error);

    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        success: false,
        error: error.message,
        metadata: {
          timestamp: new Date().toISOString(),
          environment: 'zemuresume-4gjvx1wea78e3d1e'
        }
      })
    };
  }
};

/**
 * 获取用户积木列表
 */
async function listBricks(openid, params = {}) {
  console.log('📋 获取积木列表', { openid, params });

  const { category, limit = 100, skip = 0, sortBy = 'updateTime', sortOrder = 'desc' } = params;

  let query = db.collection('bricks').where({
    _openid: openid
  });

  // 按分类筛选
  if (category) {
    query = query.where({
      category: category
    });
  }

  // 排序
  const sortDirection = sortOrder === 'desc' ? 'desc' : 'asc';
  query = query.orderBy(sortBy, sortDirection);

  // 分页
  query = query.skip(skip).limit(limit);

  const result = await query.get();

  console.log(`✅ 获取到 ${result.data.length} 个积木`);

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    },
    body: JSON.stringify({
      success: true,
      data: {
        bricks: result.data,
        total: result.data.length,
        hasMore: result.data.length === limit
      },
      metadata: {
        timestamp: new Date().toISOString(),
        environment: 'zemuresume-4gjvx1wea78e3d1e'
      }
    })
  };
}

/**
 * 添加新积木
 */
async function addBrick(openid, brickData) {
  console.log('➕ 添加积木', { openid, brickData });

  // 验证必要字段
  if (!brickData.title || !brickData.content || !brickData.category) {
    throw new Error('积木标题、内容和分类不能为空');
  }

  const now = new Date();
  const brick = {
    ...brickData,
    _openid: openid,
    id: brickData.id || `brick_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    createTime: now.toISOString(),
    updateTime: now.toISOString(),
    usageCount: brickData.usageCount || 0,
    isActive: true
  };

  const result = await db.collection('bricks').add({
    data: brick
  });

  console.log('✅ 积木添加成功', { _id: result._id });

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    },
    body: JSON.stringify({
      success: true,
      data: {
        _id: result._id,
        brick: brick
      },
      metadata: {
        timestamp: new Date().toISOString(),
        environment: 'zemuresume-4gjvx1wea78e3d1e'
      }
    })
  };
}

/**
 * 更新积木
 */
async function updateBrick(openid, updateData) {
  console.log('✏️ 更新积木', { openid, updateData });

  const { _id, id, ...updates } = updateData;

  if (!_id && !id) {
    throw new Error('积木ID不能为空');
  }

  // 构建查询条件
  let whereCondition = { _openid: openid };
  if (_id) {
    whereCondition._id = _id;
  } else {
    whereCondition.id = id;
  }

  // 添加更新时间
  updates.updateTime = new Date().toISOString();

  const result = await db.collection('bricks')
    .where(whereCondition)
    .update({
      data: updates
    });

  if (result.stats.updated === 0) {
    throw new Error('积木不存在或无权限更新');
  }

  console.log('✅ 积木更新成功', { updated: result.stats.updated });

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    },
    body: JSON.stringify({
      success: true,
      data: {
        updated: result.stats.updated
      },
      metadata: {
        timestamp: new Date().toISOString(),
        environment: 'zemuresume-4gjvx1wea78e3d1e'
      }
    })
  };
}

/**
 * 删除积木
 */
async function deleteBrick(openid, deleteData) {
  console.log('🗑️ 删除积木', { openid, deleteData });

  const { _id, id } = deleteData;

  if (!_id && !id) {
    throw new Error('积木ID不能为空');
  }

  // 构建查询条件
  let whereCondition = { _openid: openid };
  if (_id) {
    whereCondition._id = _id;
  } else {
    whereCondition.id = id;
  }

  const result = await db.collection('bricks')
    .where(whereCondition)
    .remove();

  if (result.stats.removed === 0) {
    throw new Error('积木不存在或无权限删除');
  }

  console.log('✅ 积木删除成功', { removed: result.stats.removed });

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    },
    body: JSON.stringify({
      success: true,
      data: {
        removed: result.stats.removed
      },
      metadata: {
        timestamp: new Date().toISOString(),
        environment: 'zemuresume-4gjvx1wea78e3d1e'
      }
    })
  };
}

/**
 * 获取单个积木详情
 */
async function getBrick(openid, params) {
  console.log('🔍 获取积木详情', { openid, params });

  const { _id, id } = params;

  if (!_id && !id) {
    throw new Error('积木ID不能为空');
  }

  // 构建查询条件
  let whereCondition = { _openid: openid };
  if (_id) {
    whereCondition._id = _id;
  } else {
    whereCondition.id = id;
  }

  const result = await db.collection('bricks')
    .where(whereCondition)
    .get();

  if (result.data.length === 0) {
    throw new Error('积木不存在');
  }

  console.log('✅ 获取积木详情成功');

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    },
    body: JSON.stringify({
      success: true,
      data: {
        brick: result.data[0]
      },
      metadata: {
        timestamp: new Date().toISOString(),
        environment: 'zemuresume-4gjvx1wea78e3d1e'
      }
    })
  };
}

/**
 * 同步本地积木数据到云端
 */
async function syncBricks(openid, syncData) {
  console.log('🔄 同步积木数据', { openid, syncData });

  const { bricks, mode = 'merge' } = syncData;

  if (!Array.isArray(bricks)) {
    throw new Error('积木数据必须是数组格式');
  }

  let syncResults = {
    added: 0,
    updated: 0,
    skipped: 0,
    errors: []
  };

  // 如果是覆盖模式，先清空现有数据
  if (mode === 'overwrite') {
    const deleteResult = await db.collection('bricks')
      .where({ _openid: openid })
      .remove();
    console.log(`🗑️ 覆盖模式：删除了 ${deleteResult.stats.removed} 个现有积木`);
  }

  // 逐个处理积木数据
  for (const brickData of bricks) {
    try {
      if (!brickData.title || !brickData.content) {
        syncResults.errors.push(`积木 ${brickData.id || '未知'} 缺少必要字段`);
        continue;
      }

      const now = new Date();
      const brick = {
        ...brickData,
        _openid: openid,
        id: brickData.id || `brick_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        updateTime: now.toISOString(),
        createTime: brickData.createTime || now.toISOString(),
        usageCount: brickData.usageCount || 0,
        isActive: brickData.isActive !== false
      };

      if (mode === 'merge' && brickData.id) {
        // 合并模式：检查是否已存在
        const existingResult = await db.collection('bricks')
          .where({
            _openid: openid,
            id: brickData.id
          })
          .get();

        if (existingResult.data.length > 0) {
          // 更新现有积木
          const { id, _openid, ...updates } = brick;
          await db.collection('bricks')
            .where({
              _openid: openid,
              id: brickData.id
            })
            .update({
              data: updates
            });
          syncResults.updated++;
        } else {
          // 添加新积木
          await db.collection('bricks').add({
            data: brick
          });
          syncResults.added++;
        }
      } else {
        // 覆盖模式或新积木：直接添加
        await db.collection('bricks').add({
          data: brick
        });
        syncResults.added++;
      }

    } catch (error) {
      console.error(`❌ 同步积木失败:`, error);
      syncResults.errors.push(`积木 ${brickData.id || '未知'} 同步失败: ${error.message}`);
    }
  }

  console.log('✅ 积木同步完成', syncResults);

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    },
    body: JSON.stringify({
      success: true,
      data: syncResults,
      metadata: {
        timestamp: new Date().toISOString(),
        environment: 'zemuresume-4gjvx1wea78e3d1e'
      }
    })
  };
}
